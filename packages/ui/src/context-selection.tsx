import React, { useState } from 'react';
import { Search, Plus, Minus, User, Package, Wrench, CheckSquare } from 'lucide-react';

export interface SelectableItem {
  id: string;
  name: string;
  description?: string;
  type: 'team_member' | 'catalog_item' | 'inventory_item' | 'task_template';
  category?: string;
  metadata?: Record<string, any>;
  isSelected?: boolean;
  quantity?: number;
}

export interface ContextDataResult {
  items: SelectableItem[];
  totalCount: number;
  categories?: string[];
  hasMore?: boolean;
}

interface ContextSelectionProps {
  contextData: ContextDataResult;
  onSelectionChange: (itemId: string, isSelected: boolean, quantity?: number) => void;
  onConfirmSelection: () => void;
  loading?: boolean;
  editType: string;
}

export const ContextSelection: React.FC<ContextSelectionProps> = ({
  contextData,
  onSelectionChange,
  onConfirmSelection,
  loading = false,
  editType
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Filter items based on search term and category
  const filteredItems = contextData.items.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  // Group items by category for display
  const groupedItems = filteredItems.reduce((groups, item) => {
    const category = item.category || 'Other';
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(item);
    return groups;
  }, {} as Record<string, SelectableItem[]>);

  const getItemIcon = (type: string) => {
    switch (type) {
      case 'team_member':
        return <User className="w-4 h-4" />;
      case 'catalog_item':
        return <Package className="w-4 h-4" />;
      case 'inventory_item':
        return <Wrench className="w-4 h-4" />;
      case 'task_template':
        return <CheckSquare className="w-4 h-4" />;
      default:
        return <Package className="w-4 h-4" />;
    }
  };

  const getEditTypeTitle = (editType: string) => {
    switch (editType) {
      case 'add_team_member':
        return 'Select Team Members';
      case 'add_product':
        return 'Select Products';
      case 'add_service':
        return 'Select Services';
      case 'add_material':
        return 'Select Materials';
      case 'add_task':
        return 'Select Tasks';
      default:
        return 'Select Items';
    }
  };

  const handleQuantityChange = (item: SelectableItem, newQuantity: number) => {
    if (newQuantity <= 0) {
      onSelectionChange(item.id, false, 0);
    } else {
      onSelectionChange(item.id, true, newQuantity);
    }
  };

  const selectedCount = contextData.items.filter(item => item.isSelected).length;

  const renderItemCard = (item: SelectableItem) => {
    const isSelected = item.isSelected || false;
    const quantity = item.quantity || 1;

    // Compact rendering for team members in modal context
    if (item.type === 'team_member') {
      return (
        <div
          key={item.id}
          className={`border rounded-[var(--radius-8)] p-[var(--spacing-8)] transition-all duration-200 cursor-pointer ${
            isSelected
              ? 'border-[var(--color-primary)] bg-[var(--color-primary-background)]'
              : 'border-[var(--color-stroke)] hover:border-[var(--color-stroke-hover)]'
          }`}
          onClick={() => onSelectionChange(item.id, !isSelected)}
        >
          <div className="flex items-center gap-[var(--spacing-8)]">
            {/* Compact Avatar */}
            <div className="w-10 h-10 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center flex-shrink-0">
              {item.metadata?.avatar ? (
                <img
                  src={item.metadata.avatar}
                  alt={item.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full bg-gray-300 flex items-center justify-center text-gray-600 text-xs font-medium">
                  {item.name?.split(' ').map(n => n[0]).join('').toUpperCase() || 'MM'}
                </div>
              )}
            </div>

            {/* Text Content */}
            <div className="flex-1 min-w-0">
              <div className="font-medium text-[var(--typography-body-size)] text-[var(--color-text-primary)] truncate">
                {item.name}
              </div>
              {item.description && (
                <div className="text-[var(--typography-caption-size)] text-[var(--color-text-secondary)] truncate">
                  {item.description}
                </div>
              )}
            </div>

            {/* Selection Indicator */}
            <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center flex-shrink-0 ${
              isSelected
                ? 'border-[var(--color-primary)] bg-[var(--color-primary)]'
                : 'border-[var(--color-stroke)]'
            }`}>
              {isSelected && (
                <div className="w-2 h-2 bg-white rounded-full"></div>
              )}
            </div>
          </div>
        </div>
      );
    }

    // Default rendering for other item types
    return (
      <div
        key={item.id}
        className={`border rounded-[var(--radius-8)] p-[var(--spacing-12)] transition-all duration-200 ${
          isSelected
            ? 'border-[var(--color-primary)] bg-[var(--color-primary-background)]'
            : 'border-[var(--color-stroke)] hover:border-[var(--color-stroke-hover)]'
        }`}
      >
        <div className="flex items-start justify-between mb-[var(--spacing-8)]">
          <div className="flex items-start gap-[var(--spacing-8)] flex-1">
            <div className="text-[var(--color-text-secondary)] mt-1">
              {getItemIcon(item.type)}
            </div>
            <div className="flex-1">
              <h4 className="text-[var(--typography-body-size)] font-medium text-[var(--color-text-primary)] mb-[var(--spacing-4)]">
                {item.name}
              </h4>
              {item.description && (
                <p className="text-[var(--typography-caption-size)] text-[var(--color-text-secondary)] mb-[var(--spacing-8)]">
                  {item.description}
                </p>
              )}
              {item.category && (
                <span className="inline-block px-[var(--spacing-8)] py-[var(--spacing-4)] bg-[var(--color-secondary-background)] text-[var(--typography-caption-size)] text-[var(--color-text-secondary)] rounded-[var(--radius-4)]">
                  {item.category}
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Quantity Controls */}
        {(item.type === 'catalog_item' || item.type === 'inventory_item') && (
          <div className="flex items-center justify-between mt-[var(--spacing-12)]">
            <span className="text-[var(--typography-caption-size)] text-[var(--color-text-secondary)]">
              Quantity:
            </span>
            <div className="flex items-center gap-[var(--spacing-8)]">
              <button
                onClick={() => handleQuantityChange(item, Math.max(0, quantity - 1))}
                className="w-8 h-8 rounded-[var(--radius-4)] border border-[var(--color-stroke)] flex items-center justify-center hover:bg-[var(--color-background-secondary)] transition-colors"
                disabled={loading}
              >
                <Minus className="w-3 h-3" />
              </button>
              <span className="min-w-[2rem] text-center text-[var(--typography-body-size)] font-medium">
                {quantity}
              </span>
              <button
                onClick={() => handleQuantityChange(item, quantity + 1)}
                className="w-8 h-8 rounded-[var(--radius-4)] border border-[var(--color-stroke)] flex items-center justify-center hover:bg-[var(--color-background-secondary)] transition-colors"
                disabled={loading}
              >
                <Plus className="w-3 h-3" />
              </button>
            </div>
          </div>
        )}

        {/* Selection Toggle for non-quantity items (excluding team_member as it's handled above) */}
        {item.type === 'task_template' ? (
          <div className="flex justify-end mt-[var(--spacing-12)]">
            <button
              onClick={() => onSelectionChange(item.id, !isSelected)}
              className={`px-[var(--spacing-12)] py-[var(--spacing-6)] rounded-[var(--radius-4)] text-[var(--typography-caption-size)] font-medium transition-colors ${
                isSelected
                  ? 'bg-[var(--color-primary)] text-white'
                  : 'bg-[var(--color-background-secondary)] text-[var(--color-text-primary)] hover:bg-[var(--color-background-tertiary)]'
              }`}
              disabled={loading}
            >
              {isSelected ? 'Selected' : 'Select'}
            </button>
          </div>
        ) : null}
      </div>
    );
  };

  return (
    <div className="flex flex-col h-full space-y-[var(--spacing-16)]">
      {/* Header */}
      <div className="flex items-center justify-between flex-shrink-0">
        <h3 className="text-[var(--typography-h3-size)] font-[var(--typography-h3-weight)] text-[var(--color-text-primary)]">
          {getEditTypeTitle(editType)}
        </h3>
        {selectedCount > 0 && (
          <span className="text-[var(--typography-caption-size)] text-[var(--color-text-secondary)]">
            {selectedCount} selected
          </span>
        )}
      </div>

      {/* Search and Filter */}
      <div className="flex gap-[var(--spacing-12)] flex-shrink-0">
        <div className="flex-1 relative">
          <Search className="absolute left-[var(--spacing-12)] top-1/2 transform -translate-y-1/2 w-4 h-4 text-[var(--color-text-secondary)]" />
          <input
            type="text"
            placeholder="Search items..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-[var(--spacing-36)] pr-[var(--spacing-12)] py-[var(--spacing-8)] border border-[var(--color-stroke)] rounded-[var(--radius-4)] text-[var(--typography-body-size)] placeholder:text-[var(--color-text-secondary)] focus:outline-none focus:border-[var(--color-primary)]"
          />
        </div>

        {contextData.categories && contextData.categories.length > 1 && (
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-[var(--spacing-12)] py-[var(--spacing-8)] border border-[var(--color-stroke)] rounded-[var(--radius-4)] text-[var(--typography-body-size)] focus:outline-none focus:border-[var(--color-primary)]"
          >
            <option value="all">All Categories</option>
            {contextData.categories.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>
        )}
      </div>

      {/* Items Grid - Scrollable Container */}
      <div className="flex-1 overflow-y-auto min-h-0">
        <div className="space-y-[var(--spacing-16)]">
          {Object.entries(groupedItems).map(([category, items]) => (
            <div key={category}>
              {Object.keys(groupedItems).length > 1 && (
                <h4 className="text-[var(--typography-body-size)] font-medium text-[var(--color-text-primary)] mb-[var(--spacing-12)]">
                  {category}
                </h4>
              )}
              <div className="grid grid-cols-1 gap-[var(--spacing-8)]">
                {items.map(renderItemCard)}
              </div>
            </div>
          ))}
        </div>
      </div>

      {filteredItems.length === 0 && (
        <div className="text-center py-[var(--spacing-24)] text-[var(--color-text-secondary)]">
          No items found matching your criteria.
        </div>
      )}

      {/* Confirm Button */}
      {selectedCount > 0 && (
        <div className="flex justify-end pt-[var(--spacing-16)] border-t border-[var(--color-stroke)] flex-shrink-0">
          <button
            onClick={onConfirmSelection}
            disabled={loading}
            className="px-[var(--spacing-16)] py-[var(--spacing-8)] bg-[var(--color-primary)] text-white rounded-[var(--radius-4)] text-[var(--typography-body-size)] font-medium hover:bg-[var(--color-primary-hover)] transition-colors disabled:opacity-50"
          >
            Add {selectedCount} {selectedCount === 1 ? 'Item' : 'Items'} to Project
          </button>
        </div>
      )}
    </div>
  );
};

export default ContextSelection;
